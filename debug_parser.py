#!/usr/bin/env python3
"""Debug script to analyze pattern parsing issues"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from backtesting_rule_parser import BacktestingRuleParser
import json

def main():
    # Load a real LLM response
    with open('llm_data/GBRIDXGBP/session_20250630_180159.json', 'r') as f:
        data = json.load(f)

    llm_response = data['llm_analysis']
    print("LLM Response:")
    print("=" * 50)
    print(llm_response)
    print("=" * 50)

    # Parse the rules and examine entry logic
    parser = BacktestingRuleParser()
    rules = parser.parse_llm_response(llm_response)

    print(f'\nParsed {len(rules)} rules:')
    for rule in rules:
        print(f'\nRule {rule.rule_id}: {rule.name}')
        print(f'  Entry Logic: "{rule.entry_logic}"')
        print(f'  Direction: {rule.direction}')
        print(f'  Stop Logic: "{rule.stop_logic}"')
        print(f'  Target Logic: "{rule.target_logic}"')
        
        # Test entry logic evaluation
        entry_clean = rule.entry_logic.strip().replace('`', '').lower()
        print(f'  Cleaned Entry: "{entry_clean}"')
        
        # Check what patterns it would match
        matches = []
        if 'current_close > previous_high' in entry_clean:
            matches.append('current_close > previous_high')
        if 'current_close < previous_close' in entry_clean:
            matches.append('current_close < previous_close')
        if 'current_close > previous_close' in entry_clean:
            matches.append('current_close > previous_close')
        if 'current_range > previous_range' in entry_clean:
            matches.append('current_range > previous_range (✅ NOW SUPPORTED)')
        if 'current_open > previous_close' in entry_clean:
            matches.append('current_open > previous_close (✅ NOW SUPPORTED)')
            
        if matches:
            print(f'  Matches: {", ".join(matches)}')
        else:
            print('  ❌ NO MATCHES - This is the problem!')

if __name__ == '__main__':
    main()
