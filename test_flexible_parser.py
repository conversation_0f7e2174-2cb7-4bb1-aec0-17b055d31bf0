#!/usr/bin/env python3
"""Test script for the flexible pattern parser"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from flexible_pattern_parser import FlexiblePatternParser
import pandas as pd

def test_flexible_parser():
    # Test with the problematic format from your example
    problematic_llm_response = """
### PATTERN [X]: Volatility Compression Breakout
**Core Behavioral Insight:** In low volatility regimes, after a series of contracting ranges and reduced volume, an outside day breakout often triggers rapid price movement due to accumulated institutional positions seeking resolution.

**Translation Logic:** Simplified the pattern by focusing on the critical trigger condition of an outside day following four days of decreasing daily ranges. This captures the essence of the behavioral inefficiency where retail traders are passive while institutions accumulate and then release pressure through a breakout.

**Entry Logic:** current_close > previous_high
**Direction:** long
**Stop Logic:** previous_low
**Target Logic:** entry_price + (entry_price - stop_price) * 3.0
**Position Size:** 1
**Timeframe:** 1d
**Preserved Edge:** The simplified pattern still exploits the behavioral inefficiency of retail complacency and institutional accumulation leading to a breakout.

---

### PATTERN [Y]: Range Expansion After Consolidation
**Core Behavioral Insight:** After extended sideways movement with decreasing volume, a sudden volume spike with range expansion indicates institutional accumulation completion and directional bias establishment.

**Entry Logic:** current_range > previous_range * 1.8
**Direction:** long
**Stop Logic:** previous_low
**Target Logic:** entry_price + (entry_price - stop_price) * 2.5
**Position Size:** 1
**Timeframe:** 4h
"""

    print("Testing Flexible Pattern Parser")
    print("=" * 50)
    
    parser = FlexiblePatternParser()
    
    try:
        # Debug: Check field extraction first
        field_blocks = parser._extract_field_blocks(problematic_llm_response)
        print(f"Debug: Extracted {len(field_blocks)} field blocks:")
        for i, field in enumerate(field_blocks[:10]):  # Show first 10
            print(f"  {i+1}. {field['field']}: {field['value']}")

        # Debug: Check grouping
        pattern_groups = parser._group_fields_into_patterns(field_blocks)
        print(f"\nDebug: Grouped into {len(pattern_groups)} pattern groups:")
        for i, group in enumerate(pattern_groups):
            print(f"  Group {i+1}: {group}")

        patterns = parser.parse_llm_response(problematic_llm_response)
        print(f"✅ Successfully parsed {len(patterns)} patterns!")
        
        for pattern in patterns:
            print(f"\nPattern {pattern.pattern_id}: {pattern.name}")
            print(f"  Entry Logic: {pattern.entry_logic}")
            print(f"  Direction: {pattern.direction}")
            print(f"  Stop Logic: {pattern.stop_logic}")
            print(f"  Target Logic: {pattern.target_logic}")
            print(f"  Position Size: {pattern.position_size}")
            print(f"  Timeframe: {pattern.timeframe}")
        
        # Test function generation
        functions = parser.generate_python_functions()
        print(f"\n✅ Generated {len(functions)} executable functions!")
        
        # Test with sample data
        sample_data = pd.DataFrame({
            'Open': [100.0, 101.0, 102.0, 103.0, 104.0],
            'High': [101.0, 102.0, 103.0, 104.0, 105.0],
            'Low': [99.0, 100.0, 101.0, 102.0, 103.0],
            'Close': [100.5, 101.5, 102.5, 103.5, 104.5],
            'Volume': [1000, 1100, 1200, 1300, 1400]
        })
        
        print("\nTesting function execution:")
        for i, func in enumerate(functions):
            try:
                result = func(sample_data, 2)
                if result:
                    print(f"  Function {i+1}: ✅ Generated signal: {result}")
                else:
                    print(f"  Function {i+1}: ❌ No signal (condition not met)")
            except Exception as e:
                print(f"  Function {i+1}: ❌ Error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Parsing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_flexible_parser()
    if success:
        print("\n🎉 Flexible parser test PASSED!")
    else:
        print("\n💥 Flexible parser test FAILED!")
