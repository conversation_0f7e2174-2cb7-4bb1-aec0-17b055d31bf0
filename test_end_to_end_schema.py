#!/usr/bin/env python3
"""End-to-end test of the new schema-based pattern parsing system"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import json
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>

def test_end_to_end_system():
    """Test the complete pipeline with schema-based patterns"""
    
    print("🎯 END-TO-END SCHEMA-BASED PATTERN SYSTEM TEST")
    print("=" * 70)
    
    # Create comprehensive test patterns
    comprehensive_patterns = [
        {
            "pattern_name": "Volatility Compression Breakout",
            "description": "Exploits volatility expansion after multi-day compression",
            "market_situation": "Low volatility environment with range contraction",
            "entry_conditions": [
                {
                    "condition": "consecutive_days",
                    "type": "range_contraction", 
                    "periods": 4,
                    "threshold": 0.20
                },
                {
                    "condition": "close_above_high",
                    "lookback": 1
                }
            ],
            "entry_logic": "AND",
            "exit_conditions": [
                {
                    "condition": "risk_reward_ratio",
                    "risk": 1,
                    "reward": 3
                }
            ],
            "filters": [
                {
                    "condition": "low_volatility_regime",
                    "lookback": 20,
                    "threshold": 0.005
                }
            ],
            "position_sizing": {
                "method": "fixed_percent",
                "value": 0.02,
                "max_risk": 0.02
            },
            "statistical_edge": {
                "win_rate": 0.75,
                "avg_risk_reward": 2.5,
                "sample_size": 150,
                "confidence_level": 0.85
            },
            "behavioral_logic": "Institutional accumulation during quiet periods leads to retail stop-loss cascades on breakout"
        },
        {
            "pattern_name": "Range Expansion After Consolidation",
            "description": "Captures momentum after sideways movement",
            "entry_conditions": [
                {
                    "condition": "range_expansion",
                    "threshold": 1.8,
                    "lookback": 5
                }
            ],
            "exit_conditions": [
                {
                    "condition": "risk_reward_ratio",
                    "risk": 1,
                    "reward": 2.5
                }
            ],
            "position_sizing": {
                "method": "fixed_percent",
                "value": 0.015
            }
        }
    ]
    
    json_response = json.dumps(comprehensive_patterns, indent=2)
    
    # Test 1: Schema-based parsing
    print("\n1. 🔍 Testing Schema-Based Pattern Parsing")
    print("-" * 50)
    
    try:
        from backtesting_rule_parser import SchemaBasedPatternParser
        
        parser = SchemaBasedPatternParser()
        patterns = parser.parse_llm_response(json_response)
        
        print(f"   ✅ Parsed {len(patterns)} patterns successfully")
        
        for i, pattern in enumerate(patterns, 1):
            print(f"   Pattern {i}: {pattern.pattern_name}")
            print(f"     Entry Conditions: {len(pattern.entry_conditions)}")
            print(f"     Exit Conditions: {len(pattern.exit_conditions)}")
            print(f"     Filters: {len(pattern.filters)}")
            print(f"     Backward Compatibility:")
            print(f"       rule_id: {pattern.rule_id}")
            print(f"       direction: {pattern.direction}")
            print(f"       position_size: {pattern.position_size}")
            print()
        
    except Exception as e:
        print(f"   ❌ Schema parsing failed: {e}")
        return False
    
    # Test 2: Function generation
    print("2. ⚙️  Testing Function Generation")
    print("-" * 50)
    
    try:
        functions = parser.generate_python_functions()
        print(f"   ✅ Generated {len(functions)} executable functions")
        
        # Test with sample data
        dates = [datetime.now() - timedelta(days=i) for i in range(100, 0, -1)]
        sample_data = pd.DataFrame({
            'Open': [100.0 + i*0.1 for i in range(100)],
            'High': [101.0 + i*0.1 for i in range(100)],
            'Low': [99.0 + i*0.1 for i in range(100)],
            'Close': [100.5 + i*0.1 for i in range(100)],
            'Volume': [1000 + i*10 for i in range(100)]
        }, index=pd.DatetimeIndex(dates))
        
        signals_generated = 0
        for i, func in enumerate(functions):
            try:
                result = func(sample_data, 50)  # Test with middle index
                if result:
                    signals_generated += 1
                    print(f"   Function {i+1}: ✅ Signal generated")
                    print(f"     Entry: {result['entry_price']:.2f}")
                    print(f"     Stop: {result['stop_loss']:.2f}")
                    print(f"     Target: {result['take_profit']:.2f}")
                else:
                    print(f"   Function {i+1}: ⚪ No signal (conditions not met)")
            except Exception as e:
                print(f"   Function {i+1}: ❌ Error: {e}")
        
        print(f"   📊 Total signals generated: {signals_generated}/{len(functions)}")
        
    except Exception as e:
        print(f"   ❌ Function generation failed: {e}")
        return False
    
    # Test 3: Backward compatibility functions
    print("\n3. 🔄 Testing Backward Compatibility")
    print("-" * 50)
    
    try:
        from backtesting_rule_parser import parse_backtesting_rules, BacktestingRuleParser
        
        # Test parse_backtesting_rules function
        rule_functions = parse_backtesting_rules(json_response)
        print(f"   ✅ parse_backtesting_rules: {len(rule_functions)} functions")
        
        # Test BacktestingRuleParser._extract_patterns
        parser = BacktestingRuleParser()
        individual_patterns = parser._extract_patterns(json_response)
        print(f"   ✅ _extract_patterns: {len(individual_patterns)} patterns")
        
        for i, pattern_name in enumerate(individual_patterns, 1):
            print(f"     Pattern {i}: {pattern_name}")
        
    except Exception as e:
        print(f"   ❌ Backward compatibility failed: {e}")
        return False
    
    # Test 4: Walk-forward validation integration
    print("\n4. 📈 Testing Walk-Forward Validation Integration")
    print("-" * 50)
    
    try:
        from pattern_walkforward_backtester import BacktestingWalkForwardValidator
        
        validator = BacktestingWalkForwardValidator(min_return_threshold=1.0)
        result = validator.validate_patterns(json_response, sample_data)
        
        print(f"   ✅ Walk-forward validation completed")
        print(f"     Success: {result.get('success', 'Unknown')}")
        print(f"     Total patterns: {result.get('total_patterns', 'Unknown')}")
        print(f"     Profitable patterns: {len(result.get('profitable_patterns', []))}")
        print(f"     Success rate: {result.get('success_rate', 'Unknown')}%")
        
    except Exception as e:
        print(f"   ❌ Walk-forward validation failed: {e}")
        return False
    
    # Test 5: Error handling
    print("\n5. 🛡️  Testing Error Handling")
    print("-" * 50)
    
    try:
        # Test with invalid JSON
        invalid_json = "This is not valid JSON"
        
        try:
            parser.parse_llm_response(invalid_json)
            print("   ❌ Should have failed with invalid JSON")
        except Exception as e:
            print(f"   ✅ Correctly handled invalid JSON: {type(e).__name__}")
        
        # Test with missing required fields
        invalid_pattern = {"pattern_name": "Incomplete Pattern"}
        invalid_json = json.dumps(invalid_pattern)
        
        try:
            parser.parse_llm_response(invalid_json)
            print("   ❌ Should have failed with missing fields")
        except Exception as e:
            print(f"   ✅ Correctly handled missing fields: {type(e).__name__}")
        
    except Exception as e:
        print(f"   ❌ Error handling test failed: {e}")
        return False
    
    print("\n" + "=" * 70)
    print("🎉 END-TO-END SCHEMA-BASED SYSTEM TEST PASSED!")
    print("\n✅ Key Achievements:")
    print("  ✓ Schema-based JSON parsing (no more regex hell)")
    print("  ✓ Structured pattern representation")
    print("  ✓ Executable function generation")
    print("  ✓ Backward compatibility maintained")
    print("  ✓ Walk-forward validation integration")
    print("  ✓ Robust error handling")
    print("  ✓ Multiple pattern support")
    print("  ✓ Extensible condition system")
    
    return True

if __name__ == '__main__':
    success = test_end_to_end_system()
    if success:
        print("\n🚀 Schema-based pattern system is ready for production!")
    else:
        print("\n💥 Schema-based pattern system needs fixes!")
