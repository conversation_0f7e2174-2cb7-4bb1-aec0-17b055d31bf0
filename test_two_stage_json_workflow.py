#!/usr/bin/env python3
"""Test the complete two-stage workflow with JSON schema output"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import json
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>

def test_two_stage_workflow():
    """Test the complete two-stage workflow with JSON schema compatibility"""
    
    print("🎯 TESTING TWO-STAGE JSON SCHEMA WORKFLOW")
    print("=" * 60)
    
    # Test 1: Stage 1 Prompt Generation
    print("\n1. 🎨 Testing Stage 1 Discovery Prompt Generation")
    print("-" * 50)
    
    try:
        from ai_integration.situational_prompts import TomHougaardDiscoveryPrompts
        
        # Create sample OHLC data
        dates = [datetime.now() - timedelta(days=i) for i in range(100, 0, -1)]
        sample_data = pd.DataFrame({
            'Open': [100.0 + i*0.1 for i in range(100)],
            'High': [101.0 + i*0.1 for i in range(100)],
            'Low': [99.0 + i*0.1 for i in range(100)],
            'Close': [100.5 + i*0.1 for i in range(100)],
            'Volume': [1000 + i*10 for i in range(100)]
        }, index=pd.DatetimeIndex(dates))
        
        stage1_prompt = TomHougaardDiscoveryPrompts.generate_stage1_discovery_prompt(
            ohlc_data=sample_data,
            market_summaries="Sample behavioral analysis",
            performance_feedback="Sample learning data"
        )
        
        print(f"   ✅ Stage 1 prompt generated: {len(stage1_prompt)} characters")
        print(f"   ✅ Contains Tom Hougaard methodology: {'Tom Hougaard' in stage1_prompt}")
        print(f"   ✅ Contains discovery examples: {'DISCOVERY EXAMPLES' in stage1_prompt}")
        
    except Exception as e:
        print(f"   ❌ Stage 1 prompt generation failed: {e}")
        return False
    
    # Test 2: Stage 2 JSON Schema Prompt Generation
    print("\n2. 🔧 Testing Stage 2 JSON Schema Translation Prompt")
    print("-" * 50)
    
    try:
        from ai_integration.pattern_translation_prompts import PatternTranslationPrompts
        
        # Simulate Stage 1 output
        stage1_output = """
        PATTERN 1: Volatility Compression Breakout
        Market Situation: Low volatility environment with 4+ days of range contraction
        Participant Behavior: Institutional accumulation during quiet periods, retail FOMO on breakout
        Statistical Edge: 75% win rate with 2.5:1 average risk-reward over 150 samples
        Behavioral Logic: Compressed volatility creates coiled spring effect with predictable expansion
        
        PATTERN 2: Failed Breakout Reversal
        Market Situation: High-volume session with new highs that fail to hold key levels
        Participant Behavior: Retail panic selling after failed breakout, institutional profit-taking
        Statistical Edge: 68% win rate with 2:1 risk-reward during overlap sessions
        Behavioral Logic: Failed breakouts trigger stop-loss cascades and momentum reversal
        """
        
        stage2_prompt = PatternTranslationPrompts.generate_stage2_translation_prompt(stage1_output)
        
        print(f"   ✅ Stage 2 prompt generated: {len(stage2_prompt)} characters")
        print(f"   ✅ Contains JSON schema format: {'JSON' in stage2_prompt}")
        print(f"   ✅ Contains condition types: {'entry_conditions' in stage2_prompt}")
        print(f"   ✅ Contains translation examples: {'JSON Output:' in stage2_prompt}")
        
    except Exception as e:
        print(f"   ❌ Stage 2 prompt generation failed: {e}")
        return False
    
    # Test 3: Simulate LLM JSON Response and Parse
    print("\n3. 📊 Testing JSON Schema Response Parsing")
    print("-" * 50)
    
    try:
        # Simulate what the LLM should output from Stage 2
        simulated_llm_response = [
            {
                "pattern_name": "Volatility Compression Breakout",
                "description": "Exploits volatility expansion after multi-day compression",
                "market_situation": "Low volatility environment with range contraction over 4+ days",
                "entry_conditions": [
                    {
                        "condition": "consecutive_days",
                        "type": "range_contraction",
                        "periods": 4,
                        "threshold": 0.20
                    },
                    {
                        "condition": "close_above_high",
                        "lookback": 1
                    }
                ],
                "entry_logic": "AND",
                "exit_conditions": [
                    {
                        "condition": "risk_reward_ratio",
                        "risk": 1,
                        "reward": 2.5
                    }
                ],
                "position_sizing": {
                    "method": "fixed_percent",
                    "value": 0.02,
                    "max_risk": 0.02
                },
                "behavioral_logic": "Institutional accumulation during quiet periods leads to retail FOMO on breakout"
            },
            {
                "pattern_name": "Failed Breakout Reversal",
                "description": "Exploits retail panic after failed breakout during high-volume sessions",
                "entry_conditions": [
                    {
                        "condition": "close_below_low",
                        "lookback": 1
                    }
                ],
                "exit_conditions": [
                    {
                        "condition": "risk_reward_ratio",
                        "risk": 1,
                        "reward": 2.0
                    }
                ],
                "behavioral_logic": "Failed breakouts trigger stop-loss cascades and momentum reversal"
            }
        ]
        
        json_response = json.dumps(simulated_llm_response, indent=2)
        print(f"   ✅ Simulated LLM JSON response: {len(json_response)} characters")
        
        # Test parsing with schema-based parser
        from backtesting_rule_parser import SchemaBasedPatternParser
        
        parser = SchemaBasedPatternParser()
        patterns = parser.parse_llm_response(json_response)
        
        print(f"   ✅ Successfully parsed {len(patterns)} patterns")
        
        for i, pattern in enumerate(patterns, 1):
            print(f"   Pattern {i}: {pattern.pattern_name}")
            print(f"     Entry conditions: {len(pattern.entry_conditions)}")
            print(f"     Exit conditions: {len(pattern.exit_conditions)}")
            print(f"     Behavioral logic: {pattern.behavioral_logic[:50]}...")
        
    except Exception as e:
        print(f"   ❌ JSON schema parsing failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test 4: Function Generation and Execution
    print("\n4. ⚙️  Testing Function Generation from JSON Patterns")
    print("-" * 50)
    
    try:
        functions = parser.generate_python_functions()
        print(f"   ✅ Generated {len(functions)} executable functions")
        
        # Test with sample data
        dates = [datetime.now() - timedelta(days=i) for i in range(50, 0, -1)]
        test_data = pd.DataFrame({
            'Open': [100.0 + i*0.1 for i in range(50)],
            'High': [101.0 + i*0.1 for i in range(50)],
            'Low': [99.0 + i*0.1 for i in range(50)],
            'Close': [100.5 + i*0.1 for i in range(50)],
            'Volume': [1000 + i*10 for i in range(50)]
        }, index=pd.DatetimeIndex(dates))
        
        signals_generated = 0
        for i, func in enumerate(functions):
            try:
                result = func(test_data, 25)  # Test with middle index
                if result:
                    signals_generated += 1
                    print(f"   Function {i+1}: ✅ Signal - Entry: {result['entry_price']:.2f}, Stop: {result['stop_loss']:.2f}, Target: {result['take_profit']:.2f}")
                else:
                    print(f"   Function {i+1}: ⚪ No signal (conditions not met)")
            except Exception as e:
                print(f"   Function {i+1}: ❌ Error: {e}")
        
        print(f"   📊 Total signals generated: {signals_generated}/{len(functions)}")
        
    except Exception as e:
        print(f"   ❌ Function generation failed: {e}")
        return False
    
    # Test 5: Walk-Forward Validation Integration
    print("\n5. 📈 Testing Walk-Forward Validation with JSON Patterns")
    print("-" * 50)
    
    try:
        from pattern_walkforward_backtester import BacktestingWalkForwardValidator
        
        validator = BacktestingWalkForwardValidator(min_return_threshold=1.0)
        result = validator.validate_patterns(json_response, test_data)
        
        print(f"   ✅ Walk-forward validation completed")
        print(f"     Success: {result.get('success', 'Unknown')}")
        print(f"     Total patterns: {result.get('total_patterns', 'Unknown')}")
        print(f"     Profitable patterns: {len(result.get('profitable_patterns', []))}")
        print(f"     Success rate: {result.get('success_rate', 'Unknown')}%")
        
    except Exception as e:
        print(f"   ❌ Walk-forward validation failed: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 TWO-STAGE JSON SCHEMA WORKFLOW TEST PASSED!")
    print("\n✅ Complete Workflow Verified:")
    print("  ✓ Stage 1: Tom Hougaard discovery prompt generation")
    print("  ✓ Stage 2: JSON schema translation prompt generation")
    print("  ✓ JSON schema pattern parsing and validation")
    print("  ✓ Executable function generation from JSON patterns")
    print("  ✓ Walk-forward validation integration")
    print("  ✓ End-to-end compatibility with existing pipeline")
    
    return True

if __name__ == '__main__':
    success = test_two_stage_workflow()
    if success:
        print("\n🚀 Two-stage JSON schema workflow is ready for production!")
    else:
        print("\n💥 Two-stage JSON schema workflow needs fixes!")
