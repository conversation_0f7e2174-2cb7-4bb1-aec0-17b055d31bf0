#!/usr/bin/env python3
"""Test cortex.py integration with new schema-based parser"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import json

def test_cortex_functions():
    """Test the functions that cortex.py calls"""
    
    print("Testing Cortex Integration with Schema-Based Parser")
    print("=" * 60)
    
    # Test JSON pattern
    json_pattern = {
        "pattern_name": "Cortex Test Pattern",
        "entry_conditions": [
            {
                "condition": "close_above_high",
                "lookback": 1
            }
        ],
        "exit_conditions": [
            {
                "condition": "risk_reward_ratio",
                "risk": 1,
                "reward": 2.5
            }
        ]
    }
    
    json_response = json.dumps(json_pattern, indent=2)
    
    # Test 1: parse_backtesting_rules function
    print("\n1. Testing parse_backtesting_rules function:")
    try:
        from backtesting_rule_parser import parse_backtesting_rules
        
        rule_functions = parse_backtesting_rules(json_response)
        print(f"   ✅ Successfully parsed {len(rule_functions)} rule functions")
        
        if rule_functions:
            print(f"   ✅ Function type: {type(rule_functions[0])}")
        
    except Exception as e:
        print(f"   ❌ parse_backtesting_rules failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 2: extract_individual_patterns function (via BacktestingRuleParser)
    print("\n2. Testing extract_individual_patterns function:")
    try:
        from backtesting_rule_parser import BacktestingRuleParser
        
        parser = BacktestingRuleParser()
        individual_patterns = parser._extract_patterns(json_response)
        print(f"   ✅ Successfully extracted {len(individual_patterns)} individual patterns")
        
        for i, pattern in enumerate(individual_patterns):
            print(f"   Pattern {i+1}: {pattern}")
        
    except Exception as e:
        print(f"   ❌ extract_individual_patterns failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 3: BacktestingRuleParseError import
    print("\n3. Testing BacktestingRuleParseError import:")
    try:
        from backtesting_rule_parser import BacktestingRuleParseError
        print(f"   ✅ Successfully imported BacktestingRuleParseError: {BacktestingRuleParseError}")
        
    except Exception as e:
        print(f"   ❌ BacktestingRuleParseError import failed: {e}")
    
    # Test 4: Test with multiple patterns (array format)
    print("\n4. Testing with multiple patterns:")
    try:
        multiple_patterns = [json_pattern, {
            "pattern_name": "Second Test Pattern",
            "entry_conditions": [
                {
                    "condition": "range_expansion",
                    "threshold": 1.5
                }
            ],
            "exit_conditions": [
                {
                    "condition": "fixed_take_profit",
                    "percentage": 0.03
                }
            ]
        }]
        
        multiple_json = json.dumps(multiple_patterns, indent=2)
        
        rule_functions = parse_backtesting_rules(multiple_json)
        print(f"   ✅ Successfully parsed {len(rule_functions)} rule functions from multiple patterns")
        
        parser = BacktestingRuleParser()
        individual_patterns = parser._extract_patterns(multiple_json)
        print(f"   ✅ Successfully extracted {len(individual_patterns)} individual patterns")
        
    except Exception as e:
        print(f"   ❌ Multiple patterns test failed: {e}")
    
    # Test 5: Test with malformed JSON (should handle gracefully)
    print("\n5. Testing error handling with malformed input:")
    try:
        malformed_input = "This is not JSON and should be handled gracefully"
        
        rule_functions = parse_backtesting_rules(malformed_input)
        print(f"   ✅ Gracefully handled malformed input: {len(rule_functions)} functions returned")
        
        parser = BacktestingRuleParser()
        individual_patterns = parser._extract_patterns(malformed_input)
        print(f"   ✅ Gracefully handled malformed input: {len(individual_patterns)} patterns returned")
        
    except Exception as e:
        print(f"   ⚠️  Error handling test: {e}")
    
    print("\n" + "=" * 60)
    print("✅ Cortex integration testing complete!")
    print("\nKey compatibility features verified:")
    print("  - parse_backtesting_rules() function")
    print("  - BacktestingRuleParser._extract_patterns() method")
    print("  - BacktestingRuleParseError exception")
    print("  - Multiple pattern support")
    print("  - Error handling for malformed input")

if __name__ == '__main__':
    test_cortex_functions()
