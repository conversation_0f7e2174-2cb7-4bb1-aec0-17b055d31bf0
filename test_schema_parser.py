#!/usr/bin/env python3
"""Test script for the new schema-based pattern parser"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from backtesting_rule_parser import SchemaBasedPatternParser
import pandas as pd
import json

def test_schema_parser():
    # Test with structured JSON pattern
    json_pattern = {
        "pattern_name": "Volatility Compression Breakout",
        "description": "Exploits volatility expansion after multi-day compression in low volatility regimes",
        "market_situation": "Low volatility environment with range contraction over 4+ days",
        "entry_conditions": [
            {
                "condition": "consecutive_days",
                "type": "range_contraction", 
                "periods": 4,
                "threshold": 0.20
            },
            {
                "condition": "close_above_high",
                "lookback": 1
            }
        ],
        "entry_logic": "AND",
        "exit_conditions": [
            {
                "condition": "risk_reward_ratio",
                "risk": 1,
                "reward": 3
            }
        ],
        "filters": [
            {
                "condition": "low_volatility_regime",
                "lookback": 20,
                "threshold": 0.005
            }
        ],
        "position_sizing": {
            "method": "fixed_percent",
            "value": 0.02,
            "max_risk": 0.02
        },
        "statistical_edge": {
            "win_rate": 0.75,
            "avg_risk_reward": 2.5,
            "sample_size": 150,
            "confidence_level": 0.85
        },
        "behavioral_logic": "Institutional accumulation during quiet periods leads to retail stop-loss cascades on breakout"
    }

    print("Testing Schema-Based Pattern Parser")
    print("=" * 50)
    
    parser = SchemaBasedPatternParser()
    
    try:
        # Test JSON parsing
        json_response = json.dumps(json_pattern, indent=2)
        print("JSON Input:")
        print(json_response[:200] + "...")
        print()
        
        patterns = parser.parse_llm_response(json_response)
        print(f"✅ Successfully parsed {len(patterns)} patterns!")
        
        for pattern in patterns:
            print(f"\nPattern: {pattern.pattern_name}")
            print(f"  Entry Conditions: {len(pattern.entry_conditions)}")
            print(f"  Exit Conditions: {len(pattern.exit_conditions)}")
            print(f"  Entry Logic: {pattern.entry_logic}")
            print(f"  Filters: {len(pattern.filters)}")
            print(f"  Description: {pattern.description[:100]}...")
        
        # Test function generation
        functions = parser.generate_python_functions()
        print(f"\n✅ Generated {len(functions)} executable functions!")
        
        # Test with sample data
        sample_data = pd.DataFrame({
            'Open': [100.0 + i*0.1 for i in range(50)],
            'High': [101.0 + i*0.1 for i in range(50)],
            'Low': [99.0 + i*0.1 for i in range(50)],
            'Close': [100.5 + i*0.1 for i in range(50)],
            'Volume': [1000 + i*10 for i in range(50)]
        })
        
        print("\nTesting function execution:")
        for i, func in enumerate(functions):
            try:
                result = func(sample_data, 25)  # Test with index 25
                if result:
                    print(f"  Function {i+1}: ✅ Generated signal")
                    print(f"    Entry: {result['entry_price']:.2f}")
                    print(f"    Stop: {result['stop_loss']:.2f}")
                    print(f"    Target: {result['take_profit']:.2f}")
                    print(f"    Direction: {result['direction']}")
                    print(f"    Size: {result['position_size']}")
                else:
                    print(f"  Function {i+1}: ❌ No signal (conditions not met)")
            except Exception as e:
                print(f"  Function {i+1}: ❌ Error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Parsing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_patterns():
    """Test with multiple patterns in array format"""
    print("\n" + "=" * 50)
    print("Testing Multiple Patterns")
    print("=" * 50)
    
    multiple_patterns = [
        {
            "pattern_name": "Range Expansion Breakout",
            "entry_conditions": [
                {
                    "condition": "range_expansion",
                    "threshold": 1.8,
                    "lookback": 5
                }
            ],
            "exit_conditions": [
                {
                    "condition": "risk_reward_ratio",
                    "risk": 1,
                    "reward": 2.5
                }
            ]
        },
        {
            "pattern_name": "Inside Day Reversal",
            "entry_conditions": [
                {
                    "condition": "inside_day"
                },
                {
                    "condition": "close_above_high"
                }
            ],
            "entry_logic": "AND",
            "exit_conditions": [
                {
                    "condition": "fixed_take_profit",
                    "percentage": 0.03
                }
            ]
        }
    ]
    
    parser = SchemaBasedPatternParser()
    
    try:
        json_response = json.dumps(multiple_patterns, indent=2)
        patterns = parser.parse_llm_response(json_response)
        
        print(f"✅ Successfully parsed {len(patterns)} patterns from array!")
        
        for i, pattern in enumerate(patterns, 1):
            print(f"\nPattern {i}: {pattern.pattern_name}")
            print(f"  Entry Conditions: {len(pattern.entry_conditions)}")
            print(f"  Exit Conditions: {len(pattern.exit_conditions)}")
        
        functions = parser.generate_python_functions()
        print(f"\n✅ Generated {len(functions)} executable functions from multiple patterns!")
        
        return True
        
    except Exception as e:
        print(f"❌ Multiple pattern parsing failed: {e}")
        return False

if __name__ == '__main__':
    success1 = test_schema_parser()
    success2 = test_multiple_patterns()
    
    if success1 and success2:
        print("\n🎉 Schema-based parser tests PASSED!")
        print("\n✅ Key Benefits Achieved:")
        print("  - No more regex pattern matching")
        print("  - Structured JSON input/output")
        print("  - Extensible condition system")
        print("  - Multiple pattern support")
        print("  - Backward compatibility maintained")
    else:
        print("\n💥 Schema-based parser tests FAILED!")
